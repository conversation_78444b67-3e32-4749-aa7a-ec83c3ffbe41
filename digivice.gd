extends Node2D

# 常量定义 - 游戏平衡参数
const STAT_MIN = 0
const STAT_MAX = 100
const INITIAL_HUNGER = 50
const INITIAL_HAPPINESS = 50
const INITIAL_HEALTH = 100
const INITIAL_AGE = 0
const INITIAL_LEVEL = 1

# 时间相关常量
const UPDATE_INTERVAL = 10.0  # 每10秒更新一次

# 状态变化常量
const HUNGER_DECAY_RATE = 3
const HAPPINESS_DECAY_RATE = 1
const LOW_HUNGER_THRESHOLD = 20
const LOW_HAPPINESS_THRESHOLD = 20
const CRITICAL_HEALTH_THRESHOLD = 20
const VERY_HUNGRY_THRESHOLD = 10

# 进化条件常量
const EVOLUTION_AGE_REQUIREMENT = 30
const EVOLUTION_HUNGER_REQUIREMENT = 70
const EVOLUTION_HAPPINESS_REQUIREMENT = 70
const EVOLUTION_HEALTH_REQUIREMENT = 80

# 行动效果常量
const FEED_HUNGER_GAIN = 25
const FEED_HAPPINESS_GAIN = 5
const PLAY_HAPPINESS_GAIN = 20
const PLAY_HUNGER_COST = 8
const PLAY_HEALTH_GAIN = 2
const PLAY_MIN_HUNGER = 30
const HEAL_HEALTH_GAIN = 30
const HEAL_HAPPINESS_GAIN = 10
const SLEEP_HAPPINESS_GAIN = 15
const SLEEP_HEALTH_GAIN = 10

# 数码宝贝属性
var hunger: int = INITIAL_HUNGER
var happiness: int = INITIAL_HAPPINESS
var health: int = INITIAL_HEALTH
var age: int = INITIAL_AGE
var level: int = INITIAL_LEVEL

# 时间系统
var game_timer: float = 0.0

# 游戏状态
var is_sleeping: bool = false
var evolution_ready: bool = false

# UI 节点引用
@onready var status_label: Label = $UI/StatusPanel/StatusLabel
@onready var pet_display: Label = $UI/PetDisplay
@onready var feed_button: Button = $UI/ButtonPanel/FeedButton
@onready var play_button: Button = $UI/ButtonPanel/PlayButton
@onready var heal_button: Button = $UI/ButtonPanel/HealButton
@onready var sleep_button: Button = $UI/ButtonPanel/SleepButton
@onready var evolve_button: Button = $UI/ButtonPanel/EvolveButton

# 缓存的状态文本，避免重复构建
var cached_status_text: String = ""
var last_update_hash: int = 0

# 调试和日志控制
var debug_mode: bool = true
var log_actions: bool = true

# 在常量定义部分添加日志级别常量
const LOG_LEVEL_DEBUG = 0
const LOG_LEVEL_INFO = 1
const LOG_LEVEL_WARNING = 2
const LOG_LEVEL_ERROR = 3

# 在变量定义部分添加日志相关变量
var current_log_level: int = LOG_LEVEL_INFO  # 默认日志级别
var log_to_file: bool = false  # 是否将日志保存到文件
var log_file_path: String = "user://digivice_log.txt"  # 日志文件路径
var _log_file = null  # 日志文件对象

# 信号定义 - 用于解耦UI更新和游戏逻辑
signal pet_status_changed(hunger: int, happiness: int, health: int, age: int, level: int)
signal pet_action_performed(action_name: String, success: bool, message: String)
signal evolution_available(ready: bool)

func _ready() -> void:
	_log_message("=== Digivice 启动 ===", LOG_LEVEL_INFO)
	_log_message("欢迎来到数码世界！", LOG_LEVEL_INFO)
	_log_message("您的数码宝贝正在等待您的照顾...", LOG_LEVEL_INFO)
	
	# 初始化日志系统
	_init_log_system()

	# 验证 UI 节点是否存在
	if not _validate_ui_nodes():
		_log_message("UI 节点初始化失败！", LOG_LEVEL_ERROR)
		push_error("UI 节点初始化失败！")
		return

	# 连接按钮信号
	_connect_button_signals()

	# 连接内部信号
	_connect_internal_signals()

	# 初始化显示
	_refresh_display()
	_log_message("提示：您可以点击按钮或使用键盘操作", LOG_LEVEL_INFO)

func _init_log_system() -> void:
	"""初始化日志系统，如果需要则打开日志文件"""
	if log_to_file:
		_open_log_file()
	_log_message("日志系统初始化完成，当前日志级别：" + _get_log_level_name(current_log_level), LOG_LEVEL_DEBUG)

func _open_log_file() -> void:
	"""打开日志文件准备写入"""
	_log_file = FileAccess.open(log_file_path, FileAccess.WRITE)
	if _log_file:
		_log_file.store_string("=== Digivice 日志开始 ===\n")
		_log_file.store_string("时间: " + Time.get_datetime_string_from_system() + "\n\n")
	else:
		push_error("无法打开日志文件：" + log_file_path)

func _close_log_file() -> void:
	"""关闭日志文件"""
	if _log_file:
		_log_file.close()
		_log_file = null

func _get_log_level_name(level: int) -> String:
	"""根据日志级别返回对应的名称"""
	match level:
		LOG_LEVEL_DEBUG:
			return "DEBUG"
		LOG_LEVEL_INFO:
			return "INFO"
		LOG_LEVEL_WARNING:
			return "WARNING"
		LOG_LEVEL_ERROR:
			return "ERROR"
		_:
			return "UNKNOWN"

func _log_message(message: String, level: int = LOG_LEVEL_DEBUG) -> void:
	"""统一的日志输出函数，支持日志级别和文件输出"""
	if level < current_log_level:
		return  # 低于当前日志级别的消息不输出
		
	var timestamp = Time.get_datetime_string_from_system(true)
	var level_name = _get_log_level_name(level)
	var formatted_message = "[%s] [%s] %s" % [timestamp, level_name, message]
	
	# 控制台输出
	if debug_mode or level >= LOG_LEVEL_INFO:
		print(formatted_message)
	
	# 文件输出
	if log_to_file and _log_file:
		_log_file.store_string(formatted_message + "\n")

func _log_action(action: String, success: bool, message: String) -> void:
	"""记录玩家行动"""
	if log_actions:
		var status = "✅" if success else "❌"
		_log_message("%s [%s] %s" % [status, action, message])

func _connect_internal_signals() -> void:
	"""连接内部信号用于解耦"""
	pet_status_changed.connect(_on_pet_status_changed)
	pet_action_performed.connect(_on_pet_action_performed)
	evolution_available.connect(_on_evolution_available)

func _validate_ui_nodes() -> bool:
	"""验证所有必需的 UI 节点是否存在"""
	var nodes_to_check = [
		status_label, pet_display, feed_button,
		play_button, heal_button, sleep_button, evolve_button
	]

	for node in nodes_to_check:
		if not node:
			push_error("缺少必需的 UI 节点")
			return false
	return true

func _connect_button_signals() -> void:
	"""连接所有按钮信号"""
	feed_button.pressed.connect(feed_pet)
	play_button.pressed.connect(play_with_pet)
	heal_button.pressed.connect(heal_pet)
	sleep_button.pressed.connect(put_to_sleep)
	evolve_button.pressed.connect(evolve_pet)

func _process(delta: float) -> void:
	# 游戏主循环
	game_timer += delta
	if game_timer >= UPDATE_INTERVAL:
		game_timer = 0.0
		_update_pet_status()

func _update_pet_status() -> void:
	"""更新数码宝贝状态的核心逻辑"""
	var old_stats = [hunger, happiness, health, age, level]

	# 随时间变化的属性
	if not is_sleeping:
		_apply_time_decay()

	# 状态相互影响
	_apply_status_effects()

	# 年龄增长
	age += 1

	# 检查进化条件
	_check_evolution()

	# 发出状态变化信号（如果有变化）
	var new_stats = [hunger, happiness, health, age, level]
	if old_stats != new_stats:
		pet_status_changed.emit(hunger, happiness, health, age, level)

	# 更新显示
	_refresh_display()
	_check_warnings()

func _apply_time_decay() -> void:
	"""应用时间流逝的属性衰减"""
	hunger = _clamp_stat(hunger - HUNGER_DECAY_RATE)
	happiness = _clamp_stat(happiness - HAPPINESS_DECAY_RATE)

func _apply_status_effects() -> void:
	"""应用状态之间的相互影响"""
	# 饥饿影响健康和快乐
	if hunger < LOW_HUNGER_THRESHOLD:
		happiness = _clamp_stat(happiness - 2)
		health = _clamp_stat(health - 1)

	# 不快乐影响健康
	if happiness < LOW_HAPPINESS_THRESHOLD:
		health = _clamp_stat(health - 1)

func _clamp_stat(value: int) -> int:
	"""确保属性值在有效范围内"""
	return clamp(value, STAT_MIN, STAT_MAX)

func _refresh_display() -> void:
	"""刷新所有显示内容"""
	_update_ui_display()
	_show_status()

func _update_ui_display() -> void:
	"""更新UI界面显示，使用缓存优化性能"""
	var current_hash = _get_status_hash()

	# 只有状态发生变化时才重新构建文本
	if current_hash != last_update_hash:
		last_update_hash = current_hash
		cached_status_text = _build_status_text()

	if status_label:
		status_label.text = cached_status_text

	if pet_display:
		pet_display.text = _get_current_pet_emoji()

func _get_status_hash() -> int:
	"""生成当前状态的哈希值，用于检测变化"""
	return str([hunger, happiness, health, age, level, is_sleeping, evolution_ready]).hash()

func _build_status_text() -> String:
	"""构建状态文本"""
	var status_text = "🎮 数码宝贝状态 🎮\n\n"
	status_text += "等级: %d | 年龄: %d\n" % [level, age]
	status_text += "🍽️ 饥饿值: %d/100\n" % hunger
	status_text += "😊 快乐值: %d/100\n" % happiness
	status_text += "💚 健康值: %d/100\n\n" % health
	status_text += "状态: %s" % _get_current_status_text()
	return status_text

func _get_current_status_text() -> String:
	"""获取当前状态描述"""
	if is_sleeping:
		return "😴 睡眠中"
	elif evolution_ready:
		return "✨ 准备进化！"
	elif health <= CRITICAL_HEALTH_THRESHOLD:
		return "🤒 生病"
	elif hunger <= LOW_HUNGER_THRESHOLD:
		return "😋 饥饿"
	elif happiness <= LOW_HAPPINESS_THRESHOLD:
		return "😢 不开心"
	else:
		return "😊 正常"

func _get_current_pet_emoji() -> String:
	"""获取当前宠物表情"""
	if is_sleeping:
		return "😴"
	elif evolution_ready:
		return "✨"
	elif health <= CRITICAL_HEALTH_THRESHOLD:
		return "🤒"
	elif hunger <= LOW_HUNGER_THRESHOLD:
		return "😋"
	elif happiness <= LOW_HAPPINESS_THRESHOLD:
		return "😢"
	else:
		return _get_pet_emoji_by_level()

func _get_pet_emoji_by_level() -> String:
	"""根据等级返回不同的数码宝贝图标"""
	match level:
		1:
			return "🥚"  # 蛋
		2:
			return "🐣"  # 幼体
		3:
			return "🐾"  # 成长期
		4:
			return "🦄"  # 成熟期
		_:
			return "🐉"  # 完全体

func _show_status() -> void:
	"""在控制台显示状态信息"""
	_log_message("\n=== 数码宝贝状态 ===")
	_log_message("等级: %d | 年龄: %d" % [level, age])
	_log_message("饥饿值: %d/100" % hunger)
	_log_message("快乐值: %d/100" % happiness)
	_log_message("健康值: %d/100" % health)
	_log_message("状态: %s" % _get_current_status_text())
	_log_message("==================")

func _check_warnings() -> void:
	"""检查并显示警告"""
	if health <= CRITICAL_HEALTH_THRESHOLD:
		_log_message("⚠️  警告：数码宝贝健康状况危险！")
	elif hunger <= VERY_HUNGRY_THRESHOLD:
		_log_message("🍽️  提醒：数码宝贝非常饿了！")
	elif happiness <= LOW_HAPPINESS_THRESHOLD:
		_log_message("😢 提醒：数码宝贝看起来不开心...")

func _check_evolution() -> void:
	"""检查进化条件"""
	var can_evolve = _is_evolution_possible()

	if can_evolve and not evolution_ready:
		evolution_ready = true
		evolution_available.emit(true)
		_log_message("🌟 恭喜！您的数码宝贝准备进化了！")

func _is_evolution_possible() -> bool:
	"""检查是否满足进化条件"""
	return (
		age >= EVOLUTION_AGE_REQUIREMENT and
		hunger >= EVOLUTION_HUNGER_REQUIREMENT and
		happiness >= EVOLUTION_HAPPINESS_REQUIREMENT and
		health >= EVOLUTION_HEALTH_REQUIREMENT
	)

# 以下是可以通过按键或UI调用的功能函数
func feed_pet() -> void:
	"""喂食功能"""
	if not _validate_action_conditions("feed"):
		return

	if hunger >= 90:
		pet_action_performed.emit("喂食", false, "数码宝贝已经很饱了！")
		return

	hunger = _clamp_stat(hunger + FEED_HUNGER_GAIN)
	happiness = _clamp_stat(happiness + FEED_HAPPINESS_GAIN)
	pet_action_performed.emit("喂食", true, "喂食完成！数码宝贝很开心！")
	_refresh_display()

func play_with_pet() -> void:
	"""游戏功能"""
	if not _validate_action_conditions("play"):
		return

	if happiness >= 90:
		pet_action_performed.emit("游戏", false, "数码宝贝已经很开心了！")
		return

	if hunger < PLAY_MIN_HUNGER:
		pet_action_performed.emit("游戏", false, "数码宝贝太饿了，没有力气玩耍...")
		return

	happiness = _clamp_stat(happiness + PLAY_HAPPINESS_GAIN)
	hunger = _clamp_stat(hunger - PLAY_HUNGER_COST)
	health = _clamp_stat(health + PLAY_HEALTH_GAIN)
	pet_action_performed.emit("游戏", true, "游戏时间！数码宝贝玩得很开心！")
	_refresh_display()

func heal_pet() -> void:
	"""治疗功能"""
	if not _validate_action_conditions("heal"):
		return

	if health >= 90:
		pet_action_performed.emit("治疗", false, "数码宝贝很健康！")
		return

	health = _clamp_stat(health + HEAL_HEALTH_GAIN)
	happiness = _clamp_stat(happiness + HEAL_HAPPINESS_GAIN)
	pet_action_performed.emit("治疗", true, "治疗完成！数码宝贝恢复了健康！")
	_refresh_display()

func _validate_action_conditions(action: String) -> bool:
	"""验证行动是否可以执行的通用条件"""
	# 可以在这里添加通用的验证逻辑，比如宠物是否存活等
	if health <= 0:
		pet_action_performed.emit(action, false, "数码宝贝已经无法响应...")
		return false
	return true

func put_to_sleep() -> void:
	"""睡眠功能"""
	if is_sleeping:
		_wake_up_pet()
	else:
		_put_pet_to_sleep()

	_refresh_display()

func _wake_up_pet() -> void:
	"""唤醒宠物"""
	is_sleeping = false
	print("☀️  数码宝贝醒来了！精神饱满！")
	happiness = _clamp_stat(happiness + SLEEP_HAPPINESS_GAIN)
	health = _clamp_stat(health + SLEEP_HEALTH_GAIN)
	if sleep_button:
		sleep_button.text = "😴 睡眠 (按键4)"

func _put_pet_to_sleep() -> void:
	"""让宠物睡觉"""
	is_sleeping = true
	print("🌙 数码宝贝开始睡觉了...")
	if sleep_button:
		sleep_button.text = "☀️ 唤醒 (按键4)"

func evolve_pet() -> void:
	"""进化功能"""
	if not evolution_ready:
		print("❌ 进化条件尚未满足！")
		print("需要：年龄≥%d, 饥饿值≥%d, 快乐值≥%d, 健康值≥%d" % [
			EVOLUTION_AGE_REQUIREMENT, EVOLUTION_HUNGER_REQUIREMENT,
			EVOLUTION_HAPPINESS_REQUIREMENT, EVOLUTION_HEALTH_REQUIREMENT
		])
		return

	_perform_evolution()
	_refresh_display()

func _perform_evolution() -> void:
	"""执行进化过程"""
	level += 1
	age = INITIAL_AGE
	evolution_ready = false
	hunger = 80
	happiness = 80
	health = STAT_MAX

	print("🎉 恭喜！数码宝贝进化到了 %d 级！" % level)
	print("✨数码宝贝变得更强了！")

func reset_pet() -> void:
	"""重置数码宝贝"""
	_reset_all_stats()
	print("🔄 数码宝贝已重置到初始状态")
	_refresh_display()

func _reset_all_stats() -> void:
	"""重置所有属性到初始状态"""
	hunger = INITIAL_HUNGER
	happiness = INITIAL_HAPPINESS
	health = INITIAL_HEALTH
	age = INITIAL_AGE
	level = INITIAL_LEVEL
	is_sleeping = false
	evolution_ready = false
	if sleep_button:
		sleep_button.text = "😴 睡眠 (按键4)"

# 输入处理（用于测试）
func _input(event: InputEvent) -> void:
	if event is InputEventKey and event.pressed:
		_handle_keyboard_input(event.keycode)

func _handle_keyboard_input(keycode: Key) -> void:
	"""处理键盘输入"""
	match keycode:
		KEY_1:
			feed_pet()
		KEY_2:
			play_with_pet()
		KEY_3:
			heal_pet()
		KEY_4:
			put_to_sleep()
		KEY_5:
			evolve_pet()
		KEY_R:
			reset_pet()
		KEY_S:
			_show_status()

# 游戏开始时显示控制说明
func _notification(what: int) -> void:
	if what == NOTIFICATION_READY:
		_show_control_instructions()

func _show_control_instructions() -> void:
	"""显示控制说明"""
	print("\n=== 控制说明 ===")
	print("按键 1: 喂食 🍖")
	print("按键 2: 游戏 🎮")
	print("按键 3: 治疗 💊")
	print("按键 4: 睡眠/唤醒 😴")
	print("按键 5: 进化 ✨")
	print("按键 R: 重置 🔄")
	print("按键 S: 显示状态 📊")
	print("===============")

func _on_pet_status_changed(hunger_val: int, happiness_val: int, health_val: int, age_val: int, level_val: int) -> void:
	"""处理宠物状态变化信号"""
	# 更新UI显示
	_refresh_display()
	
	# 可以在这里添加额外的UI效果，如状态变化动画
	if health_val <= CRITICAL_HEALTH_THRESHOLD and health_val > 0:
		_log_message("数码宝贝健康状况不佳！", LOG_LEVEL_WARNING)

func _on_pet_action_performed(action_name: String, success: bool, message: String) -> void:
	"""处理宠物行动执行信号"""
	# 记录行动
	_log_action(action_name, success, message)
	
	# 显示行动结果消息
	print(message)
	
	# 更新UI显示
	_refresh_display()

func _on_evolution_available(ready: bool) -> void:
	"""处理进化可用信号"""
	# 更新进化按钮状态
	if evolve_button:
		evolve_button.disabled = !ready
		
	if ready:
		# 可以添加视觉或音效提示
		_log_message("✨ 您的数码宝贝可以进化了！", LOG_LEVEL_INFO)
		# 可以在这里添加进化按钮闪烁效果等



