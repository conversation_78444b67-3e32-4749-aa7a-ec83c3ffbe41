extends Node2D

# 数码宝贝属性
var hunger = 50      # 饥饿值 (0-100)
var happiness = 50   # 快乐值 (0-100)
var health = 100     # 健康值 (0-100)
var age = 0          # 年龄
var level = 1        # 等级

# 时间系统
var game_timer = 0.0
var update_interval = 10.0  # 每10秒更新一次

# 游戏状态
var is_sleeping = false
var evolution_ready = false

# UI 节点引用
@onready var status_label = $UI/StatusPanel/StatusLabel
@onready var pet_display = $UI/PetDisplay
@onready var feed_button = $UI/ButtonPanel/FeedButton
@onready var play_button = $UI/ButtonPanel/PlayButton
@onready var heal_button = $UI/ButtonPanel/HealButton
@onready var sleep_button = $UI/ButtonPanel/SleepButton
@onready var evolve_button = $UI/ButtonPanel/EvolveButton

func _ready():
	print("=== Digivice 启动 ===")
	print("欢迎来到数码世界！")
	print("您的数码宝贝正在等待您的照顾...")
	
	# 连接按钮信号
	feed_button.pressed.connect(feed_pet)
	play_button.pressed.connect(play_with_pet)
	heal_button.pressed.connect(heal_pet)
	sleep_button.pressed.connect(put_to_sleep)
	evolve_button.pressed.connect(evolve_pet)
	
	# 初始化显示
	update_ui_display()
	show_status()
	print("提示：您可以点击按钮或使用键盘操作")

func _process(delta):
	# 游戏主循环
	game_timer += delta
	if game_timer >= update_interval:
		game_timer = 0.0
		update_pet_status()

func update_pet_status():
	# 随时间变化的属性
	if not is_sleeping:
		hunger = max(hunger - 3, 0)
		happiness = max(happiness - 1, 0)
	
	# 饥饿影响健康和快乐
	if hunger < 20:
		happiness = max(happiness - 2, 0)
		health = max(health - 1, 0)
	
	# 不快乐影响健康
	if happiness < 20:
		health = max(health - 1, 0)
	
	# 年龄增长
	age += 1
	
	# 检查进化条件
	check_evolution()
	
	# 更新显示
	update_ui_display()
	show_status()
	check_warnings()

func update_ui_display():
	# 更新UI界面显示
	var status_text = "🎮 数码宝贝状态 🎮\n\n"
	status_text += "等级: %d | 年龄: %d\n" % [level, age]
	status_text += "🍽️ 饥饿值: %d/100\n" % hunger
	status_text += "😊 快乐值: %d/100\n" % happiness
	status_text += "💚 健康值: %d/100\n\n" % health
	
	if is_sleeping:
		status_text += "状态: 😴 睡眠中"
		pet_display.text = "😴"
	elif evolution_ready:
		status_text += "状态: ✨ 准备进化！"
		pet_display.text = "✨"
	elif health <= 20:
		status_text += "状态: 🤒 生病"
		pet_display.text = "🤒"
	elif hunger <= 20:
		status_text += "状态: 😋 饥饿"
		pet_display.text = "😋"
	elif happiness <= 20:
		status_text += "状态: 😢 不开心"
		pet_display.text = "😢"
	else:
		status_text += "状态: 😊 正常"
		pet_display.text = get_pet_emoji()
	
	status_label.text = status_text

func get_pet_emoji():
	# 根据等级返回不同的数码宝贝图标
	match level:
		1:
			return "🥚"  # 蛋
		2:
			return "🐣"  # 幼体
		3:
			return "🐾"  # 成长期
		4:
			return "🦄"  # 成熟期
		_:
			return "🐉"  # 完全体

func show_status():
	print("\n=== 数码宝贝状态 ===")
	print("等级: %d | 年龄: %d" % [level, age])
	print("饥饿值: %d/100" % hunger)
	print("快乐值: %d/100" % happiness)
	print("健康值: %d/100" % health)
	if is_sleeping:
		print("状态: 😴 睡眠中")
	elif evolution_ready:
		print("状态: ✨ 准备进化！")
	else:
		print("状态: 😊 正常")
	print("==================")

func check_warnings():
	# 检查并显示警告
	if health <= 20:
		print("⚠️  警告：数码宝贝健康状况危险！")
	elif hunger <= 10:
		print("🍽️  提醒：数码宝贝非常饿了！")
	elif happiness <= 20:
		print("😢 提醒：数码宝贝看起来不开心...")

func check_evolution():
	# 检查进化条件
	if age >= 30 and hunger >= 70 and happiness >= 70 and health >= 80:
		if not evolution_ready:
			evolution_ready = true
			print("🌟 恭喜！您的数码宝贝准备进化了！")

# 以下是可以通过按键或UI调用的功能函数
func feed_pet():
	"""喂食功能"""
	if hunger >= 90:
		print("🍽️  数码宝贝已经很饱了！")
		return
	
	hunger = min(hunger + 25, 100)
	happiness = min(happiness + 5, 100)
	print("🍖 喂食完成！数码宝贝很开心！")
	update_ui_display()
	show_status()

func play_with_pet():
	"""游戏功能"""
	if happiness >= 90:
		print("😊 数码宝贝已经很开心了！")
		return
	
	if hunger < 30:
		print("😴 数码宝贝太饿了，没有力气玩耍...")
		return
	
	happiness = min(happiness + 20, 100)
	hunger = max(hunger - 8, 0)
	health = min(health + 2, 100)
	print("�� 游戏时间！数码宝贝玩得很开心！")
	update_ui_display()
	show_status()

func heal_pet():
	"""治疗功能"""
	if health >= 90:
		print("💚 数码宝贝很健康！")
		return
	
	health = min(health + 30, 100)
	happiness = min(happiness + 10, 100)
	print("💊 治疗完成！数码宝贝恢复了健康！")
	update_ui_display()
	show_status()

func put_to_sleep():
	"""睡眠功能"""
	if is_sleeping:
		is_sleeping = false
		print("☀️  数码宝贝醒来了！精神饱满！")
		happiness = min(happiness + 15, 100)
		health = min(health + 10, 100)
		sleep_button.text = "😴 睡眠 (按键4)"
	else:
		is_sleeping = true
		print("🌙 数码宝贝开始睡觉了...")
		sleep_button.text = "☀️ 唤醒 (按键4)"
	
	update_ui_display()
	show_status()

func evolve_pet():
	"""进化功能"""
	if not evolution_ready:
		print("❌ 进化条件尚未满足！")
		print("需要：年龄≥30, 饥饿值≥70, 快乐值≥70, 健康值≥80")
		return
	
	level += 1
	age = 0
	evolution_ready = false
	hunger = 80
	happiness = 80
	health = 100
	
	print("🎉 恭喜！数码宝贝进化到了 %d 级！" % level)
	print("✨数码宝贝变得更强了！")
	update_ui_display()
	show_status()

func reset_pet():
	"""重置数码宝贝"""
	hunger = 50
	happiness = 50
	health = 100
	age = 0
	level = 1
	is_sleeping = false
	evolution_ready = false
	sleep_button.text = "😴 睡眠 (按键4)"
	print("🔄 数码宝贝已重置到初始状态")
	update_ui_display()
	show_status()

# 输入处理（用于测试）
func _input(event):
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_1:
				feed_pet()
			KEY_2:
				play_with_pet()
			KEY_3:
				heal_pet()
			KEY_4:
				put_to_sleep()
			KEY_5:
				evolve_pet()
			KEY_R:
				reset_pet()
			KEY_S:
				show_status()

# 游戏开始时显示控制说明
func _notification(what):
	if what == NOTIFICATION_READY:
		print("\n=== 控制说明 ===")
		print("按键 1: 喂食 🍖")
		print("按键 2: 游戏 🎮")
		print("按键 3: 治疗 💊")
		print("按键 4: 睡眠/唤醒 😴")
		print("按键 5: 进化 ✨")
		print("按键 R: 重置 🔄")
		print("按键 S: 显示状态 📊")
		print("===============")
