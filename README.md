# Digivice 项目

## 项目简介

这是一个使用 Godot 4.4 引擎开发的 Digivice（数码宝贝机）项目。项目目前处于初始开发阶段，包含基本的项目结构和配置。

## 技术栈

- **游戏引擎**: Godot 4.4
- **渲染方式**: Mobile 渲染器
- **主要语言**: GDScript
- **目标平台**: 移动设备

## 项目结构

```
digivice/
├── .godot/                 # Godot 引擎生成的缓存文件夹
├── digivice.gd            # 主游戏脚本文件
├── digivice.tscn          # 主游戏场景文件
├── project.godot          # Godot 项目配置文件
├── icon.svg               # 项目图标
├── .gitignore             # Git 忽略文件配置
├── .gitattributes         # Git 属性配置
└── .editorconfig          # 编辑器配置文件
```

## 核心文件说明

### digivice.gd
主游戏逻辑脚本，继承自 Node2D。目前为空白模板，等待功能实现。

### digivice.tscn
主游戏场景文件，包含一个 Node2D 节点，绑定了 digivice.gd 脚本。

### project.godot
项目配置文件，定义了：
- 项目名称：Digivice
- 主场景：digivice.tscn
- 目标功能：Godot 4.4 + Mobile
- 渲染方式：Mobile 渲染器

## 开发环境要求

- Godot 4.4 或更高版本
- 支持移动设备开发的环境配置

## 安装与运行

1. 确保已安装 Godot 4.4 或更高版本
2. 克隆或下载项目到本地
3. 在 Godot 编辑器中打开项目
4. 点击运行按钮或按 F5 启动项目

## 开发状态

🚧 **项目状态**: 初始开发阶段

目前项目包含：
- ✅ 基本项目结构
- ✅ Godot 4.4 配置
- ✅ 移动设备优化设置
- ⏳ 游戏逻辑实现（待开发）
- ⏳ UI 界面设计（待开发）
- ⏳ Digivice 功能实现（待开发）

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/新功能`)
3. 提交更改 (`git commit -am '添加新功能'`)
4. 推送到分支 (`git push origin feature/新功能`)
5. 创建 Pull Request

## 版本控制

项目使用 Git 进行版本控制，已配置适合 Godot 项目的 `.gitignore` 文件。

## 许可证

请根据项目需要添加适当的许可证信息。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建 Issue
- 提交 Pull Request

---

*最后更新时间：2024年* 