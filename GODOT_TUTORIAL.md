# Godot 新手教程 - Digivice 项目开发指南

## 目录
1. [Godot 引擎介绍](#godot-引擎介绍)
2. [安装和设置](#安装和设置)
3. [Godot 编辑器界面](#godot-编辑器界面)
4. [基础概念](#基础概念)
5. [GDScript 基础](#gdscript-基础)
6. [Digivice 项目实战](#digivice-项目实战)
7. [常用功能和技巧](#常用功能和技巧)
8. [调试和测试](#调试和测试)
9. [发布项目](#发布项目)

---

## Godot 引擎介绍

### 什么是 Godot？
Godot 是一个免费、开源的游戏引擎，特别适合：
- 2D 和 3D 游戏开发
- 跨平台发布（PC、移动设备、Web）
- 轻量级且易于学习
- 拥有自己的脚本语言 GDScript

### 为什么选择 Godot？
- **免费开源**：完全免费，无版权费用
- **轻量级**：下载大小小，启动快速
- **易学易用**：对新手友好的界面和文档
- **跨平台**：一次开发，多平台发布

---

## 安装和设置

### 1. 下载 Godot
1. 访问 [Godot 官网](https://godotengine.org/)
2. 下载 Godot 4.4 或更高版本
3. 选择适合您操作系统的版本

### 2. 安装步骤
- **Windows**: 下载 .exe 文件，直接运行即可
- **Mac**: 下载 .dmg 文件，拖拽到应用程序文件夹
- **Linux**: 下载 .AppImage 文件，添加执行权限后运行

### 3. 首次启动
1. 启动 Godot
2. 点击"导入"按钮
3. 选择您的 Digivice 项目文件夹
4. 点击"导入并编辑"

---

## Godot 编辑器界面

### 主要区域介绍

```
┌─────────────────────────────────────────────────────────┐
│  菜单栏 (File, Edit, Project, Debug, etc.)              │
├─────────────────────────────────────────────────────────┤
│  工具栏 (播放按钮, 场景切换等)                            │
├──────────┬──────────────────────────┬───────────────────┤
│          │                          │                   │
│  场景面板  │      主视口区域            │    检查器面板       │
│          │   (Scene Viewport)       │   (Inspector)     │
│  (Scene) │                          │                   │
│          │                          │                   │
├──────────┼──────────────────────────┼───────────────────┤
│          │                          │                   │
│ 文件系统   │      输出/调试区域          │    节点组/历史      │
│(FileSystem)│                        │                   │
│          │                          │                   │
└──────────┴──────────────────────────┴───────────────────┘
```

### 重要面板说明
- **场景面板**: 显示当前场景的节点树结构
- **检查器面板**: 显示选中节点的属性和设置
- **文件系统**: 显示项目中的所有文件和资源
- **主视口**: 游戏场景的可视化编辑区域

---

## 基础概念

### 1. 节点 (Node)
- Godot 中的基本构建块
- 每个节点都有特定的功能
- 节点可以有子节点，形成树状结构

### 2. 场景 (Scene)
- 节点的集合，保存为 .tscn 文件
- 可以包含其他场景作为子场景
- 游戏由多个场景组成

### 3. 脚本 (Script)
- 为节点添加行为和逻辑
- 主要使用 GDScript 语言
- 脚本文件扩展名为 .gd

### 4. 常用节点类型
- **Node2D**: 2D 游戏的基础节点
- **Control**: UI 界面元素的基础
- **Sprite2D**: 显示 2D 图像
- **Label**: 显示文本
- **Button**: 按钮控件

---

## GDScript 基础

### 1. 基本语法

```gdscript
# 这是注释
extends Node2D  # 继承自 Node2D

# 变量声明
var health = 100
var player_name = "数码宝贝训练师"
var is_alive = true

# 函数定义
func _ready():
	# 节点准备就绪时调用
	print("游戏开始！")

func _process(delta):
	# 每帧调用，delta 是时间间隔
	pass

# 自定义函数
func take_damage(amount):
	health -= amount
	if health <= 0:
		die()

func die():
	print("游戏结束")
```

### 2. 变量类型
```gdscript
# 基本类型
var number = 42          # 整数
var decimal = 3.14       # 浮点数
var text = "Hello"       # 字符串
var flag = true          # 布尔值

# 集合类型
var array = [1, 2, 3]    # 数组
var dict = {"key": "value"}  # 字典

# 节点引用
@onready var label = $Label  # 获取子节点
```

### 3. 控制结构
```gdscript
# 条件语句
if health > 50:
	print("健康状态良好")
elif health > 20:
	print("需要注意健康")
else:
	print("危险状态")

# 循环
for i in range(10):
	print("数字: ", i)

for item in array:
	print("物品: ", item)

while health > 0:
	# 游戏循环
	pass
```

---

## Digivice 项目实战

### 第一步：创建基本界面

1. **打开主场景**
   - 在文件系统中双击 `digivice.tscn`

2. **添加 UI 元素**
   ```
   Digivice (Node2D)
   ├── Background (Sprite2D)      # 背景图片
   ├── Screen (Control)           # 屏幕区域
   │   ├── StatusLabel (Label)    # 状态显示
   │   └── DigitalPet (Sprite2D)  # 数码宝贝图像
   └── Buttons (Control)          # 按钮区域
	   ├── FeedButton (Button)    # 喂食按钮
	   ├── PlayButton (Button)    # 游戏按钮
	   └── StatusButton (Button)  # 状态按钮
   ```

3. **添加节点步骤**
   - 右键点击 Digivice 节点
   - 选择"添加子节点"
   - 搜索并选择需要的节点类型
   - 重命名节点

### 第二步：编写基础脚本

编辑 `digivice.gd` 文件：

```gdscript
extends Node2D

# 数码宝贝属性
var hunger = 50      # 饥饿值 (0-100)
var happiness = 50   # 快乐值 (0-100)
var health = 100     # 健康值 (0-100)
var age = 0          # 年龄

# UI 节点引用
@onready var status_label = $Screen/StatusLabel
@onready var feed_button = $Buttons/FeedButton
@onready var play_button = $Buttons/PlayButton
@onready var status_button = $Buttons/StatusButton

func _ready():
	# 连接按钮信号
	feed_button.pressed.connect(_on_feed_pressed)
	play_button.pressed.connect(_on_play_pressed)
	status_button.pressed.connect(_on_status_pressed)
	
	# 开始游戏循环
	update_display()

func _on_feed_pressed():
	# 喂食功能
	hunger = min(hunger + 20, 100)
	happiness = min(happiness + 5, 100)
	print("喂食完成！")
	update_display()

func _on_play_pressed():
	# 游戏功能
	happiness = min(happiness + 15, 100)
	hunger = max(hunger - 5, 0)
	print("游戏时间！")
	update_display()

func _on_status_pressed():
	# 显示状态
	print("=== 数码宝贝状态 ===")
	print("饥饿值: ", hunger)
	print("快乐值: ", happiness)
	print("健康值: ", health)
	print("年龄: ", age)

func update_display():
	# 更新显示
	var status_text = "饥饿: %d  快乐: %d  健康: %d" % [hunger, happiness, health]
	status_label.text = status_text
```

### 第三步：添加游戏逻辑

```gdscript
# 在 digivice.gd 中添加以下代码

# 时间系统
var game_timer = 0.0
var update_interval = 5.0  # 每5秒更新一次

func _process(delta):
	game_timer += delta
	if game_timer >= update_interval:
		game_timer = 0.0
		update_pet_status()

func update_pet_status():
	# 随时间变化的属性
	hunger = max(hunger - 2, 0)
	
	if hunger < 20:
		happiness = max(happiness - 3, 0)
		health = max(health - 1, 0)
	
	# 年龄增长
	age += 1
	
	update_display()
	check_pet_condition()

func check_pet_condition():
	# 检查数码宝贝状态
	if health <= 0:
		print("数码宝贝生病了！需要照顾！")
	elif hunger <= 10:
		print("数码宝贝很饿！")
	elif happiness <= 20:
		print("数码宝贝不开心...")
```

---

## 常用功能和技巧

### 1. 信号系统
```gdscript
# 定义信号
signal pet_died
signal level_up

# 发射信号
pet_died.emit()

# 连接信号
pet_died.connect(_on_pet_died)
```

### 2. 场景切换
```gdscript
# 切换到其他场景
get_tree().change_scene_to_file("res://game_over.tscn")
```

### 3. 保存和加载
```gdscript
# 保存游戏数据
func save_game():
	var save_file = FileAccess.open("user://savegame.save", FileAccess.WRITE)
	var save_data = {
		"hunger": hunger,
		"happiness": happiness,
		"health": health,
		"age": age
	}
	save_file.store_string(JSON.stringify(save_data))
	save_file.close()

# 加载游戏数据
func load_game():
	if FileAccess.file_exists("user://savegame.save"):
		var save_file = FileAccess.open("user://savegame.save", FileAccess.READ)
		var save_data = JSON.parse_string(save_file.get_as_text())
		save_file.close()
		
		hunger = save_data.hunger
		happiness = save_data.happiness
		health = save_data.health
		age = save_data.age
```

---

## 调试和测试

### 1. 使用 print() 调试
```gdscript
print("变量值: ", variable_name)
print("函数被调用了")
```

### 2. 使用调试器
- 在代码行号左侧点击设置断点
- 按 F5 以调试模式运行
- 使用 F10 单步执行

### 3. 远程调试
- 在项目设置中启用远程调试
- 可以在移动设备上实时调试

---

## 发布项目

### 1. 导出设置
1. 项目 → 导出
2. 添加导出预设
3. 选择目标平台（Android、iOS、Windows 等）
4. 配置导出选项

### 2. Android 发布
1. 安装 Android SDK
2. 在导出设置中配置 SDK 路径
3. 生成调试密钥
4. 导出 APK 文件

### 3. 桌面发布
1. 选择对应平台预设
2. 配置图标和应用信息
3. 导出可执行文件

---

## 学习资源

### 官方资源
- [Godot 官方文档](https://docs.godotengine.org/)
- [Godot 官方教程](https://docs.godotengine.org/en/stable/getting_started/introduction/index.html)

### 社区资源
- Godot 中文社区
- YouTube 教程频道
- GitHub 开源项目

### 推荐学习路径
1. 完成官方的"你的第一个游戏"教程
2. 学习 GDScript 语法
3. 制作简单的 2D 游戏
4. 深入学习节点系统和信号
5. 学习 UI 设计和动画
6. 探索 3D 游戏开发

---

## 常见问题解答

### Q: 如何添加图片资源？
A: 将图片文件拖拽到文件系统面板，然后在 Sprite2D 节点的 Texture 属性中选择该图片。

### Q: 游戏运行时出现错误怎么办？
A: 查看输出面板的错误信息，通常会显示错误的具体位置和原因。

### Q: 如何制作动画？
A: 使用 AnimationPlayer 节点，可以为属性创建关键帧动画。

### Q: 如何添加音效？
A: 使用 AudioStreamPlayer 节点，将音频文件设置为 Stream 属性。

---

## 下一步计划

完成本教程后，您可以：
1. 为 Digivice 添加更多功能（进化系统、小游戏等）
2. 改善 UI 设计和用户体验
3. 添加音效和背景音乐
4. 实现数据持久化
5. 发布到移动设备

祝您学习愉快，创造出精彩的 Digivice 游戏！ 
