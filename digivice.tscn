[gd_scene load_steps=2 format=3 uid="uid://bmngu28fmso3e"]

[ext_resource type="Script" uid="uid://011xyixa6gsm" path="res://digivice.gd" id="1_885uu"]

[node name="Digivice" type="Node2D"]
script = ExtResource("1_885uu")

[node name="Background" type="ColorRect" parent="."]
offset_right = 800.0
offset_bottom = 600.0
color = Color(0.2, 0.3, 0.5, 1)

[node name="UI" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = 800.0
offset_bottom = 600.0
grow_horizontal = 2
grow_vertical = 2

[node name="Title" type="Label" parent="UI"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -100.0
offset_top = 20.0
offset_right = 100.0
offset_bottom = 60.0
grow_horizontal = 2
text = "🎮 Digivice 数码宝贝机"
horizontal_alignment = 1
vertical_alignment = 1

[node name="StatusPanel" type="Panel" parent="UI"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 50.0
grow_horizontal = 2
grow_vertical = 2

[node name="StatusLabel" type="Label" parent="UI/StatusPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2
text = "数码宝贝状态加载中..."
vertical_alignment = 1

[node name="PetDisplay" type="Label" parent="UI"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -50.0
offset_top = -50.0
offset_right = 50.0
offset_bottom = 50.0
grow_horizontal = 2
grow_vertical = 2
text = "🐾"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ButtonPanel" type="VBoxContainer" parent="UI"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = 80.0
offset_right = 100.0
offset_bottom = 280.0
grow_horizontal = 2
grow_vertical = 2

[node name="FeedButton" type="Button" parent="UI/ButtonPanel"]
layout_mode = 2
text = "🍖 喂食 (按键1)"

[node name="PlayButton" type="Button" parent="UI/ButtonPanel"]
layout_mode = 2
text = "🎮 游戏 (按键2)"

[node name="HealButton" type="Button" parent="UI/ButtonPanel"]
layout_mode = 2
text = "💊 治疗 (按键3)"

[node name="SleepButton" type="Button" parent="UI/ButtonPanel"]
layout_mode = 2
text = "😴 睡眠 (按键4)"

[node name="EvolveButton" type="Button" parent="UI/ButtonPanel"]
layout_mode = 2
text = "✨ 进化 (按键5)"

[node name="Instructions" type="Label" parent="UI"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -200.0
offset_top = -80.0
offset_right = 200.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 0
text = "使用按键1-5或点击按钮进行操作
按R重置，按S显示详细状态"
horizontal_alignment = 1
vertical_alignment = 1
